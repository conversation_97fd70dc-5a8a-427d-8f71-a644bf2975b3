# 背景
1.需要临时邮箱接收验证码，用于平台注册
2.我已经注册了域名，我已经使用126邮箱开启了邮箱转接服务，我已经将中转邮箱设置接收域名的邮件
3.我已经建立了服务器，这个服务需要部署在云服务器上面，但是云服务器还没有做任何的配置，后续需要你帮忙指引怎么部署在云服务器上面

# 任务
1.制作成edge插件以及云服务器部署的程序
2.实现流程
①插件：生成以该域名shengchai.dpdns.org为后缀的邮箱，随机字符串长度范围在 `8` 到 `12` 位之间，并将邮箱生成时间传递到云服务器。
②云服务器：运行部署的程序，通过POP3/SMTP服务获取中转邮箱`********************中邮箱生成时间之后邮件，将邮箱生成时间之后的邮件数据返回插件
③插件：显示数据，并按照解析规则提取验证码、链接
3.插件自定义解析规则说明
①插件：用户在插件中输入目标平台地址（发件人邮箱，例如 `<EMAIL>`）、邮件示例（提供一封来自该平台、包含验证码/链接的完整邮件内容，用于系统学习和生成解析模式）、验证码示例（从邮件示例中手动提取出的验证码，例如 `986558`）。
②用户选择是否共享该解析规则，如果不共享则保存在本地浏览器中，如果共享则上传到云服务器，共享的解析规则所有用户均可见
③插件访问本地解析规则+云服务器的解析规则，根据接收到的邮件地址进行寻找，如果有符合的解析规则，则插件在本地自动完成解析，如果没有符合的规则就空着


# 要求
1.生成的两个项目代码分别放在两个文件夹plugin、mailexe里面，避免代码混乱。
2.云服务器的资源紧张，代码占用资源要尽可能小，所以服务器端只完成邮箱接收服务，而解析规则部分的代码放在插件里面实现
3.全局节省资源的核心规则：只获取邮箱生成时间之后的邮件，生成时间之前的邮件不获取、不解析；不需要登录只需要最快、最小资源实现项目；除了共享的解析规则，后端服务器不存储任何用户数据、邮件，这些数据保存在临时变量中，当该用户的请求返回后（即每次用户刷新启动邮件服务，后端服务器根据本次请求返回数据后）就可以销毁这些变量数据；服务器本地不需要保存邮箱名称，只在每次请求调用时，记录对应的用户信息和邮箱名称、邮箱生成时间，根据这些信息获取并返回邮件
4.并发规则：该插件是多用户使用，每个用户独立维护自己的邮箱，可以根据用户id（时间戳+随机数组合）进行数据隔离，可以考虑轻量队列机制避免用户同时使用插件时出问题
5.安全：插件连接云服务的api密钥可以直接写在插件代码里，但需要注意后端权限控制，API只返回邮件数据，不涉及敏感操作 ；限制API功能范围（只读邮件，不能删除/修改）
6.高效规则：我希望在5s内，用户能获取该生成的邮箱的邮件

## 配置信息
### 1. POP3 服务器配置
插件需要连接到以下 POP3 服务器来收取邮件：
POP3 服务器地址: `pop.126.com`
POP3 端口: `995` (SSL/TLS 加密连接)
SMTP 服务器地址: `smtp.126.com`
SMTP 端口: `465` (SSL/TLS 加密连接)
### 2. 中转邮箱配置
中转邮箱用于接收临时邮箱转发邮件的真实邮箱账户：
中转邮箱地址: `<EMAIL>`
中转邮箱授权码: `TQVTv7gUbCGy9eBR` (此授权码是用于通过 POP3 协议登录邮箱的凭证，必须安全存储和使用，不应明文显示)
授权方式: POP3/SMTP服务
### 3.服务器信息
Ubuntu Server 22.04 LTS 64bit操作系统
1Panel Linux 面板
腾讯云
IP地址：IPv4(公) ************
### 4.注意
其它信息你需要的话不能胡编乱造，停止编写代码，直接询问我